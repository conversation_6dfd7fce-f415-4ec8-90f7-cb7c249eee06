<?php

namespace App;

class Documentos extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'documentos';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'creditos_id',
		'legenda',
        'arquivo',
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'creditos_id'   => 'integer|required',
        'legenda'       => 'required|max:254',
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'legenda'          => 'trim'
	);

    protected $dates = [
    ];



}
