# Use official PHP 8.1 image with FPM
FROM php:8.1-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    nginx \
    supervisor \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    zip \
    unzip \
    git \
    curl \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo_mysql zip exif pcntl bcmath opcache mbstring

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . .

# Install PHP dependencies
RUN composer install --no-interaction --optimize-autoloader --no-dev

# Install Node.js and NPM
RUN curl -fsSL https://deb.nodesource.com/setup_16.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest

# Build assets
RUN npm install && npm run prod

# Configure PHP
RUN echo '\
memory_limit=512M\
upload_max_filesize=64M\
post_max_size=64M\
max_execution_time=300\
date.timezone=America/Sao_Paulo\
' > /usr/local/etc/php/conf.d/custom.ini

# Configure Nginx
RUN echo '\
server {\
    listen 80;\
    server_name _;\
    root /var/www/public;\
\
    add_header X-Frame-Options "SAMEORIGIN";\
    add_header X-Content-Type-Options "nosniff";\
\
    index index.php;\
\
    charset utf-8;\
\
    location / {\
        try_files \$uri \$uri/ /index.php?\$query_string;\
    }\
\
    location ~ \\.php$ {\
        fastcgi_pass 127.0.0.1:9000;\
        fastcgi_index index.php;\
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;\
        include fastcgi_params;\
    }\
\
    location ~ /\.(?!well-known).* {\
        deny all;\
    }\
}\
' > /etc/nginx/conf.d/default.conf

# Configure Supervisor
RUN echo '\
[supervisord]\
nodaemon=true\
\
[program:php-fpm]\
command=php-fpm\
autostart=true\
autorestart=true\
priority=5\
stdout_logfile=/dev/stdout\
stdout_logfile_maxbytes=0\
stderr_logfile=/dev/stderr\
stderr_logfile_maxbytes=0\
\
[program:nginx]\
command=nginx -g "daemon off;"\
autostart=true\
autorestart=true\
priority=10\
stdout_logfile=/dev/stdout\
stdout_logfile_maxbytes=0\
stderr_logfile=/dev/stderr\
stderr_logfile_maxbytes=0' > /etc/supervisor/conf.d/supervisord.conf

# Expose ports
EXPOSE 80 443 9000

# Install runit for process management
RUN apt-get update && \
    apt-get install -y --no-install-recommends runit && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create runit services
RUN mkdir -p /etc/service/php-fpm /etc/service/nginx

# PHP-FPM service
RUN echo '#!/bin/bash\n\
exec php-fpm -F -R\n' > /etc/service/php-fpm/run && \
    chmod +x /etc/service/php-fpm/run

# Nginx service
RUN echo '#!/bin/bash\n\
# Create necessary directories\nmkdir -p /run/php/\n\
# Start nginx\nexec nginx -g "daemon off;"\n' > /etc/service/nginx/run && \
    chmod +x /etc/service/nginx/run

# Create a simple init script
RUN echo '#!/bin/bash\n\
# Start runit\nexec /usr/bin/runsvdir -P /etc/service\n' > /init && \
    chmod +x /init

# Create necessary directories
RUN mkdir -p /run/php/

# Start the application
CMD ["/init"]
