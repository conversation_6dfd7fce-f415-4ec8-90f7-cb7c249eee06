<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;

use App\Veiculos;
use App\Clientes;
use App\Consumidores;


class VeiculosController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Veiculos;
        if($id) {
            $registro = Veiculos::find($id);
            if(empty($registro)) {
                return redirect()->route('veiculos');
            }
        }
        if($request->session()->has('registro')) {
            $registro = new Veiculos($request->session()->get('registro'));
        }

        $dados = Veiculos::select('veiculos.*', 'consumidores.nome AS consumidor', 'clientes.nome AS cliente')
                    ->join('consumidores','consumidores.id','=','veiculos.consumidores_id')
                    ->join('clientes','clientes.id','=','consumidores.clientes_id')
                    ->orderBy('descricao', 'ASC');


        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        if($clientes_id) {
            $dados->where('consumidores.clientes_id','=',$clientes_id);
        }

        $consumidores_id = $request->get('consumidores_id',null);
        if($request->session()->get('logado') == 'consumidor') {
            $consumidores_id = $request->session()->get('logado_usuario')->id;
        }
        if($consumidores_id) {
            $dados->where('veiculos.consumidores_id','=',$consumidores_id);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(veiculos.descricao LIKE "%'.$termo.'%" OR clientes.nome LIKE "%'.$termo.'%" OR consumidores.nome LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();
		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo,
            'consumidores_id' => $consumidores_id
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $consumidores = Consumidores::where('status','=',1)->orderBy('nome','ASC')->get();

        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        $consumidores = Consumidores::select('consumidores.*', 'clientes.nome AS cliente')->join('clientes','clientes.id','=','consumidores.clientes_id')->where('consumidores.status','=',1)->orderBy('consumidores.nome','ASC');
        if($clientes_id) {
            $consumidores = $consumidores->where('clientes_id','=',$clientes_id);
        }
        $consumidores = $consumidores->get();

        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'consumidores' => $consumidores,
            'usuario_cliente' => $request->session()->get('logado_usuario')->clientes_id
        );

        return view('admin.veiculos')->with($params + ['titulopagina'=>'Veiculos']);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        if($request->session()->get('logado_usuario')->clientes_id) {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }
        $salvo = Veiculos::salvar($dados);


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $retorno = Veiculos::find($id)->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Veiculos::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }



}
