#!/bin/bash

echo "🧪 Testando Frontend da Aplicação Wincash"
echo "=========================================="

BASE_URL="http://localhost:8001"

echo ""
echo "1️⃣ Testando página principal..."
main_response=$(curl -s "$BASE_URL")
if echo "$main_response" | grep -q "Escolha uma unidade"; then
    echo "✅ Título principal encontrado"
else
    echo "❌ Título principal não encontrado"
fi

echo ""
echo "2️⃣ Verificando dropdown de cidades..."
cities_count=$(echo "$main_response" | grep -c "option value.*Sao Paulo\|Rio de Janeiro\|Belo Horizonte")
echo "   Cidades encontradas: $cities_count"
if [ $cities_count -gt 0 ]; then
    echo "✅ Dropdown de cidades funcionando"
else
    echo "❌ Dropdown de cidades vazio"
fi

echo ""
echo "3️⃣ Verificando lista de clientes..."
clients_count=$(echo "$main_response" | grep -c "unidadeitem")
echo "   Clientes encontrados: $clients_count"
if [ $clients_count -gt 0 ]; then
    echo "✅ Lista de clientes carregando"
    echo "   Clientes encontrados:"
    echo "$main_response" | grep -A 1 "unidadeitem" | grep -o "[A-Za-z].*[A-Za-z]" | head -5
else
    echo "❌ Nenhum cliente encontrado"
fi

echo ""
echo "4️⃣ Verificando recursos CSS/JS..."
css_count=$(echo "$main_response" | grep -c "\.css")
js_count=$(echo "$main_response" | grep -c "\.js")
echo "   Arquivos CSS: $css_count"
echo "   Arquivos JS: $js_count"

echo ""
echo "5️⃣ Testando links de clientes..."
first_client_url=$(echo "$main_response" | grep -o 'href="[^"]*sabor-arte[^"]*"' | head -1 | sed 's/href="//;s/"//')
if [ ! -z "$first_client_url" ]; then
    echo "   Testando: $first_client_url"
    client_response=$(curl -s "$first_client_url")
    if echo "$client_response" | grep -q "login\|entrar\|email"; then
        echo "✅ Página de cliente carregando (redirecionamento para login)"
    else
        echo "❌ Página de cliente com problema"
    fi
else
    echo "❌ Nenhum link de cliente encontrado"
fi

echo ""
echo "📊 RESUMO DO TESTE:"
echo "=================="
if [ $cities_count -gt 0 ] && [ $clients_count -gt 0 ]; then
    echo "🎉 Frontend funcionando corretamente!"
    echo "   ✅ Dropdown de cidades: OK"
    echo "   ✅ Lista de clientes: OK ($clients_count clientes)"
    echo "   ✅ Recursos CSS/JS: OK"
    echo ""
    echo "🌐 Aplicação disponível em: $BASE_URL"
else
    echo "⚠️  Frontend com problemas:"
    [ $cities_count -eq 0 ] && echo "   ❌ Dropdown de cidades vazio"
    [ $clients_count -eq 0 ] && echo "   ❌ Lista de clientes vazia"
fi
