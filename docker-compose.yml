version: '3'

services:
  # Serviço para a aplicação principal
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wincash_app
    restart: unless-stopped
    working_dir: /var/www/app
    volumes:
      - ./app:/var/www/app
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini:ro
      - ./docker/entrypoint.sh:/entrypoint.sh:ro
    networks:
      - wincash-network
    depends_on:
      - db
    entrypoint: ["/bin/bash", "/entrypoint.sh"]

  # # Serviço para a API
  # api:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: wincash_api
  #   restart: unless-stopped
  #   working_dir: /var/www/api
  #   volumes:
  #     - ./api:/var/www/api
  #     - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
  #   networks:
  #     - wincash-network
  #   depends_on:
  #     - db

  # # Serviço para abastecimento
  # abastecimento:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: wincash_abastecimento
  #   restart: unless-stopped
  #   working_dir: /var/www/abastecimento
  #   volumes:
  #     - ./abastecimento:/var/www/abastecimento
  #     - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
  #   networks:
  #     - wincash-network
  #   depends_on:
  #     - db

  # Serviço de Nginx (web server)
  nginx:
    image: nginx:1.21-alpine
    container_name: wincash_nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./app:/var/www/app:ro
      - ./docker/nginx/app.conf:/etc/nginx/conf.d/app.conf:ro
      - /dev/null:/etc/nginx/conf.d/default.conf:ro
    networks:
      - wincash-network
    depends_on:
      - app
      # - api
      # - abastecimento

  # Serviço de banco de dados
  db:
    image: mysql:5.7
    container_name: wincash_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-wincashbrcom_dev_app}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-root}
      MYSQL_PASSWORD: ${DB_PASSWORD:-root}
      MYSQL_USER: ${DB_USERNAME:-wincashbrcom_dev}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - wincash-data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
    #ports:
    #  - "3306:3306"
    networks:
      - wincash-network

# Volumes
volumes:
  wincash-data:

# Networks
networks:
  wincash-network:
    driver: bridge
