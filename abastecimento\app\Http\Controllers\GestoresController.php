<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Gestores;
use App\Clientes;


class GestoresController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {

        if($request->session()->get('logado') == 'consumidor') {
            return back()->withErrors(array('error' => 'Sem permissão'));
        }

        $registro = new Gestores;
        if($id) {
            $registro = Gestores::findOrFail($id);
        }

        $dados = Gestores::select('gestores.*','clientes.nome AS cliente')
                    ->leftJoin('clientes','clientes.id','=','gestores.clientes_id')
                    ->orderBy('nome', 'ASC');


        $clientes_id = $request->get('clientes_id',null);
        if($request->session()->get('logado') == 'cliente') {
            $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        }
        if($clientes_id) {
            $dados->where('gestores.clientes_id','=',$clientes_id);
        }


        $total = $dados->count();
		$dados = $dados->paginate(30);


        $filtros = array(
            'page' => $request->get('page',0)
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'clientes' => Clientes::orderBy('nome','ASC')->get()
        );

        return view('admin.gestores')->with($params);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        if($request->session()->get('logado') == 'cliente') {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }

        if($request->get('senha') != '') {
            $dados['senha'] = Hash::make($request->get('senha'));
        }
        else {
            unset($dados['senha']);
        }

        $salvo = Gestores::salvar($dados);

		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $retorno = Gestores::find($id)->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Gestores::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }

}
