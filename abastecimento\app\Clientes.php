<?php

namespace App;

class Clientes extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'clientes';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'nome',
        'cnpj',
        'email',
        'celular',
        'imagem',
        'url',
        'endereco',
        'cidade',
        'estado',
        'cep',
        'cor',
        'ativo'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'           => 'integer',
        'nome'         => 'required|max:254',
        'email'        => 'email|nullable',
        'url'          => 'required'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim',
        'email'         => 'trim',
        'url'           => 'trim'
	);




}
