version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        - DB_HOST=${DB_HOST}
        - DB_PORT=${DB_PORT}
        - DB_DATABASE=${DB_DATABASE}
        - DB_USERNAME=${DB_USERNAME}
        - DB_PASSWORD=${DB_PASSWORD}
    container_name: wincash-app
    restart: unless-stopped
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - PHP_IDE_CONFIG="serverName=docker"
    volumes:
      - .:/var/www
      - ./docker/php/prod.ini:/usr/local/etc/php/conf.d/prod.ini:ro
    ports:
      - "9000:9000"
    # The entrypoint and command are now handled by the Dockerfile
    cap_add:
      - SYS_PTRACE  # For debugging if needed
    security_opt:
      - seccomp:unconfined  # For debugging if needed
    networks:
      - wincash-network

  db:
    image: mysql:8.0
    container_name: wincash-db
    restart: always
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wincash-network

networks:
  wincash-network:
    driver: bridge

volumes:
  dbdata:
