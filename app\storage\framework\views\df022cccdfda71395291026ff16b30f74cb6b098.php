    <?php
    $nomerota = Request::route()->getName();
    ?>


<header>
<div id="menu" class="container-fluid p-0 pb-4 text-center">

        <?php if(isset($usuario)): ?>

        <div class="text-white mb-3 p-3" style="background-color: rgba(0,0,0,0.3)">
            Bem vindo, <?php echo e($usuario->nome); ?>


            <i class="fa fa-sign-out-alt ml-3"></i>
            <a href="<?php echo e(route('sair_app')); ?>" class="btn btn-sm btn-dark meubgcor pl-3 pr-3" style="border:none !important; margin-top: -4px">
             Sair
            </a>
        </div>


        <?php if(isset($usuario->cliente->imagem) and $usuario->cliente->imagem): ?>
            <img src="<?php echo e(url($usuario->cliente->imagem)); ?>" style="max-height: 150px; max-width: 300px" />
            <!--
            <img src="<?php echo e(url('img/logo.png?v=3')); ?>" class="float-left" style="width: 60px; margin-left: 5px" />
            -->
        <?php else: ?>
        <a href="<?php echo e(url('')); ?>">
            <img src="<?php echo e(url('img/logo.png?v=3')); ?>" style="width:300px" />
        </a>
        <?php endif; ?>

        <?php if(!isset($novasenhatoken) and 1==2): ?>
        <div class="text-white float-left mt-2 ml-4">
            <form action="<?php echo e(route('consumidores_app-mudarcadastro')); ?>" method="POST" id="mudarcadastro">
            <?php echo e(csrf_field()); ?>



            <div style="color: #777FDD; display:inline-block">Você está em:</div>

            <?php if(count($usuario->cadastros) > 1): ?>
                <select name="clienteid" id="clienteid" class="form-control meubgcor w-auto d-inline-block" onchange="$('#mudarcadastro').submit();">
                    <?php $__currentLoopData = $usuario->cadastros; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cadastro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($cadastro->cliente->id); ?>" <?php if($cadastro->cliente->id == $usuario->cliente->id): ?> selected="selected" <?php endif; ?>><?php echo e($cadastro->cliente->nome); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            <?php else: ?>
                &nbsp; &nbsp; <?php echo e($usuario->cliente->nome); ?>

            <?php endif; ?>
            </form>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <!--
        <a href="<?php echo e(url('')); ?>">
            <img src="<?php echo e(url('img/logo.png?v=3')); ?>" class="img-fluid" style="max-width:30%" />
        </a>
        -->
        <?php endif; ?>

        <div class="clearfix"></div>
</div>

<div id="alertaretorno" class="container">
    <?php if(!empty($errors->first('error'))): ?>
    <script type="text/javascript">
        $(window).on('load', function() {
            $('#myModal').modal('show');
        });
    </script>
    <div id="myModal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="alert alert-danger mb-0 shadow">
                <i class="fa fa-exclamation-triangle"></i> <?php echo e($errors->first('error')); ?>

            </div>
        </div>
    </div>
    </div>
    <?php endif; ?>

    <?php if(!empty($errors->first('success'))): ?>
    <script type="text/javascript">
        $(window).on('load', function() {
            $('#myModal').modal('show');
        });
    </script>
    <div id="myModal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="alert alert-success mb-0 shadow">
                <i class="fa fa-check-square"></i> <?php echo e($errors->first('success')); ?>

            </div>
        </div>
    </div>
    </div>
    <?php endif; ?>



</div>

</header>


<?php /**PATH /var/www/app/resources/views/partials/menu.blade.php ENDPATH**/ ?>