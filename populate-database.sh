#!/bin/bash

echo "🗄️ Populando banco de dados com dados de exemplo..."

# Conectar ao banco
DB_CONTAINER="wincash_db"
DB_USER="wincashbrcom_dev"
DB_PASS="root"
DB_NAME="wincashbrcom_dev_app"

# Função para executar SQL
execute_sql() {
    docker exec -it $DB_CONTAINER mysql -u $DB_USER -p$DB_PASS -e "USE $DB_NAME; $1"
}

echo "📋 Verificando dados existentes..."
existing_clientes=$(execute_sql "SELECT COUNT(*) FROM clientes;" 2>/dev/null | tail -1)
existing_categorias=$(execute_sql "SELECT COUNT(*) FROM categorias;" 2>/dev/null | tail -1)

echo "   Clientes existentes: $existing_clientes"
echo "   Categorias existentes: $existing_categorias"

if [ "$existing_clientes" -gt 0 ]; then
    echo "✅ Banco já possui dados de clientes!"
else
    echo "❌ Banco vazio, inserindo dados..."
    
    # Inserir categorias se não existirem
    if [ "$existing_categorias" -eq 0 ]; then
        echo "📂 Inserindo categorias..."
        execute_sql "INSERT INTO categorias (nome, created_at, updated_at) VALUES 
        ('Alimentação', NOW(), NOW()), 
        ('Farmácia', NOW(), NOW()), 
        ('Posto de Combustível', NOW(), NOW()), 
        ('Supermercado', NOW(), NOW()), 
        ('Loja de Roupas', NOW(), NOW());"
    fi
    
    # Inserir clientes
    echo "🏢 Inserindo clientes..."
    execute_sql "INSERT INTO clientes (categorias_id, nome, cnpj, email, celular, url, conversao, diasvalidade, reaiscashback, endereco, cidade, estado, cep, ativo, pontos_cadastro, created_at, updated_at) VALUES 
    (1, 'Restaurante Sabor & Arte', '12.345.678/0001-90', '<EMAIL>', '(11) 99999-1234', 'sabor-arte', 1.00, 365, 5.00, 'Rua das Flores, 123', 'Sao Paulo', 'SP', '01234-567', 1, 100, NOW(), NOW()),
    (2, 'Farmácia Vida Saudável', '23.456.789/0001-01', '<EMAIL>', '(11) 88888-5678', 'vida-saudavel', 1.50, 180, 3.00, 'Av. Principal, 456', 'Sao Paulo', 'SP', '01234-890', 1, 50, NOW(), NOW()),
    (3, 'Posto Shell Centro', '34.567.890/0001-12', '<EMAIL>', '(11) 77777-9012', 'shell-centro', 0.50, 90, 2.00, 'Rua do Comércio, 789', 'Sao Paulo', 'SP', '01234-123', 1, 25, NOW(), NOW()),
    (4, 'Supermercado Economia', '45.678.901/0001-23', '<EMAIL>', '(21) 66666-3456', 'supermercado-economia', 1.20, 365, 4.00, 'Av. Brasil, 1000', 'Rio de Janeiro', 'RJ', '20000-000', 1, 75, NOW(), NOW()),
    (5, 'Boutique Elegância', '56.789.012/0001-34', '<EMAIL>', '(31) 55555-7890', 'boutique-elegancia', 2.00, 180, 10.00, 'Rua da Moda, 200', 'Belo Horizonte', 'MG', '30000-000', 1, 150, NOW(), NOW());"
fi

echo ""
echo "📊 Verificando dados inseridos..."
execute_sql "SELECT c.nome, cat.nome as categoria, c.cidade, c.estado FROM clientes c LEFT JOIN categorias cat ON c.categorias_id = cat.id WHERE c.ativo = 1;"

echo ""
echo "🎉 Banco de dados populado com sucesso!"
echo "🌐 Acesse: http://localhost:8001"
