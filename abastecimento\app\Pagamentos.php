<?php

namespace App;

class Pagamentos extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'pagamentos';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'creditos_id',
		'codigo',
        'valor',
        'forma',
        'dtpagamento',
        'status'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'creditos_id'   => 'integer|required',
        'valor'         => 'required|numeric',
        'dtpagamento'   => 'required|date',
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'valor'          => 'amount',
        'dtpagamento'    => 'dateformat'
	);

    protected $dates = [
        'dtpagamento'
    ];



}
