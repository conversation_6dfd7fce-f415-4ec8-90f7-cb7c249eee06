#!/bin/bash
set -e

# Função para log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Ensure storage directory exists and set permissions
log "Configurando diretórios e permissões..."

# Lista de diretórios que precisam ser criados
directories=(
    "/var/www/app/storage/logs"
    "/var/www/app/storage/framework/sessions"
    "/var/www/app/storage/framework/views"
    "/var/www/app/storage/framework/cache"
    "/var/www/app/bootstrap/cache"
)

# Criar diretórios e configurar permissões
for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    chmod 775 "$dir"
    chown www-data:www-data "$dir"
done

# Configurar permissões do diretório storage
log "Configurando permissões do diretório storage..."
chown -R www-data:www-data /var/www/app/storage
chmod -R 775 /var/www/app/storage

# Configurar permissões do diretório bootstrap/cache
log "Configurando permissões do diretório bootstrap/cache..."
chown -R www-data:www-data /var/www/app/bootstrap/cache
chmod -R 775 /var/www/app/bootstrap/cache

# Criar e configurar arquivo de log
log "Configurando arquivo de log..."
touch /var/www/app/storage/logs/laravel.log
chown www-data:www-data /var/www/app/storage/logs/laravel.log
chmod 664 /var/www/app/storage/logs/laravel.log

# Verificar permissões
log "Verificando permissões..."
ls -la /var/www/app/storage/logs/laravel.log
ls -la /var/www/app/storage
ls -la /var/www/app/bootstrap/cache

# Start PHP-FPM
log "Iniciando PHP-FPM..."
exec php-fpm 