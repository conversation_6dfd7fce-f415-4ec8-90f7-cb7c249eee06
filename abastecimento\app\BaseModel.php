<?php

namespace App;

use Illuminate\Support\Facades\Validator;
use App\Http\Middleware\Filter;
use Illuminate\Support\Facades\DB;

use Illuminate\Database\Eloquent\Model;

class BaseModel extends Model
{
	/**
	 * Como padrao seta que nenhuma tabela tera controle de insert e update
	 *
	 * @var string
	 */
	public $timestamps = false;
    //public $autoincrement = false;    
    //public $incrementing = false;

	/**
	 * Metodo de filtro hidratando os fields
	 *
	 * @param  array  $data
	 * @return Library\Filters
	 */
	public static function filter(array $data = array())
	{
		return Filter::make($data, static::$filters);
	}

	/**
	 * Metodo de validacao para os models 
	 *
	 * @param  array  $data
	 * @return Illuminate\Validation
	 */
	public static function validate(array $data = array())
	{
		return Validator::make($data, static::$rules);
	}

	/**
	 * Salva ou atualiza um modelo
	 *
	 * @param  array  $data
	 * @return \Models
	 */
	public static function salvar(array $data = array())
	{
		// hidrata os valores
		$data = static::filter($data);
		// efetua a validacao
		$validate = static::validate($data);

		// verifica se houve falha
		if ($validate->fails()) {
			// retorna o erro
			return $validate->getMessageBag();
		}
        // sempre atualizar o campo
        $data['updated_at'] = date("Y-m-d H:i:s");
		// verifica qual acao
		if (!empty($data['id'])) {
			// realiza o update
			$update = self::find($data['id'])->update($data);
			// verifica se atualizou
			if (!$update) {
				throw new Exception(__('messages.update.error'));
			} 
			// retorna o objeto
			$return = self::find($data['id']);
		} else {
			// realiza o save e retorna o objeto
			$return = self::create($data);
		}

		return $return;
	}
}