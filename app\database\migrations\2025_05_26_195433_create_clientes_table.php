<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClientesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clientes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('categorias_id')->nullable();
            $table->string('nome', 254);
            $table->string('cnpj')->nullable();
            $table->string('email')->nullable();
            $table->string('celular')->nullable();
            $table->string('imagem')->nullable();
            $table->string('url');
            $table->decimal('conversao', 8, 2)->nullable();
            $table->integer('diasvalidade')->nullable();
            $table->decimal('reaiscashback', 8, 2)->nullable();
            $table->unsignedBigInteger('premios_id')->nullable();
            $table->string('endereco')->nullable();
            $table->string('cidade')->nullable();
            $table->string('estado')->nullable();
            $table->string('cep')->nullable();
            $table->boolean('ativo')->default(1);
            $table->string('bg_color')->nullable();
            $table->string('bg_color2')->nullable();
            $table->integer('pontos_cadastro')->nullable();
            $table->string('onesignal_api_key')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clientes');
    }
}
