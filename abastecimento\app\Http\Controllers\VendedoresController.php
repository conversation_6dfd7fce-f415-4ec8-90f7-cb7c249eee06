<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;

use App\Vendedores;
use App\Clientes;


class VendedoresController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Vendedores;
        if($id) {
            $registro = Vendedores::findOrFail($id);
        }
        if($request->session()->has('registro')) {
            $registro = new Vendedores($request->session()->get('registro'));
        }

        $dados = Vendedores::select('vendedores.*', 'clientes.nome AS cliente')
                    ->join('clientes','clientes.id','=','vendedores.clientes_id')
                    ->orderBy('nome', 'ASC');


        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        if($clientes_id) {
            $dados->where('vendedores.clientes_id','=',$clientes_id);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(vendedores.nome LIKE "%'.$termo.'%" OR clientes.nome LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();
		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $clientes = Clientes::where('ativo','=',1)->orderBy('nome','ASC')->get();
        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'clientes' => $clientes,
            'usuario_cliente' => $request->session()->get('logado_usuario')->clientes_id
        );

        return view('admin.vendedores')->with($params + ['titulopagina'=>'Vendedores']);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        if($request->session()->get('logado_usuario')->clientes_id) {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }
        $salvo = Vendedores::salvar($dados);


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $retorno = Vendedores::find($id)->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Vendedores::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }



}
