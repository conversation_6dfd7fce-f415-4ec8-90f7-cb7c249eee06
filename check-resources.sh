#!/bin/bash

echo "🔍 Verificando recursos da aplicação Wincash..."
echo "================================================"

BASE_URL="http://localhost:8001"

# Lista de recursos para verificar
resources=(
    "/css/reset.css"
    "/css/front.css"
    "/css/bootstrap.min.css"
    "/js/jquery-3.4.1.min.js"
    "/js/bootstrap.min.js"
    "/img/favicon.png"
    "/css/app.css"
    "/js/app.js"
)

echo "📋 Testando recursos principais:"
echo ""

success_count=0
total_count=${#resources[@]}

for resource in "${resources[@]}"; do
    echo -n "  Testando $resource ... "
    
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$resource")
    
    if [ "$status_code" = "200" ]; then
        echo "✅ OK ($status_code)"
        ((success_count++))
    else
        echo "❌ ERRO ($status_code)"
    fi
done

echo ""
echo "📊 Resultado: $success_count/$total_count recursos carregando corretamente"

if [ $success_count -eq $total_count ]; then
    echo "🎉 Todos os recursos estão funcionando!"
    echo "🌐 Aplicação disponível em: $BASE_URL"
else
    echo "⚠️  Alguns recursos não estão carregando corretamente."
fi

echo ""
echo "🔗 Testando página principal..."
main_status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL")
if [ "$main_status" = "200" ]; then
    echo "✅ Página principal: OK ($main_status)"
else
    echo "❌ Página principal: ERRO ($main_status)"
fi
