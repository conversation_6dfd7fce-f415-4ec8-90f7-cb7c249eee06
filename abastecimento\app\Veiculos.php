<?php

namespace App;

class Veiculos extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'veiculos';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = false;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'consumidores_id',
		'tipo',
        'descricao',
        'placa',
        'km',
        'ativo'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'                => 'integer',
        'consumidores_id'   => 'required|integer',
        'tipo'              => 'required|max:254',
        'descricao'         => 'required|max:254',
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'descricao'          => 'trim'
	);

    protected $dates = [
    ];

    public function consumidor()
    {
        return $this->hasOne('App\Consumidores','id','consumidores_id');
    }


}
