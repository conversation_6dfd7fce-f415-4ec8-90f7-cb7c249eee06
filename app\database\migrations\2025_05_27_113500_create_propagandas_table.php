<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePropagandasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('propagandas', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clientes_id');
            $table->string('titulo');
            $table->text('descricao')->nullable();
            $table->string('imagem')->nullable();
            $table->string('link')->nullable();
            $table->date('dtinicio');
            $table->date('dtfim');
            $table->boolean('ativo')->default(true);
            $table->timestamps();

            $table->foreign('clientes_id')->references('id')->on('clientes')->onDelete('cascade');
            $table->index(['clientes_id', 'dtinicio', 'dtfim']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('propagandas');
    }
}
