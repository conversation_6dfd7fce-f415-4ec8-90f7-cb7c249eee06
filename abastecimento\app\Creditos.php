<?php

namespace App;

class Creditos extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'creditos';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'consumidores_id',
		'veiculos_id',
		'vendedores_id',
		'motoristas_id',
		'tipo',
		'quantidade',
		'valor',
        'dtcompra',
        'comentarios',
        'km',
        'token',
        'status'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'                => 'integer',
        'consumidores_id'   => 'required|integer',
        'veiculos_id'   	=> 'nullable|integer',
        'vendedores_id'   	=> 'nullable|integer',
        'motoristas_id'   	=> 'nullable|integer',
        'quantidade'        => 'nullable|numeric',
        'valor'             => 'required|numeric',
        'dtcompra'          => 'required|date',
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'comentarios'    => 'trim',
        'valor'          => 'amount',
        //'quantidade'     => 'amount',
        'dtcompra'       => 'dateformat'
	);

    protected $dates = [
        'dtcompra'
    ];


    public function consumidor()
    {
        return $this->hasOne('App\Consumidores','id','consumidores_id');
    }

    public function veiculo()
    {
        return $this->hasOne('App\Veiculos','id','veiculos_id');
    }

    public function vendedor()
    {
        return $this->hasOne('App\Vendedores','id','vendedores_id');
    }

    public function motorista()
    {
        return $this->hasOne('App\Motoristas','id','motoristas_id');
    }

    public function pagamentos()
    {
        return $this->hasMany('App\Pagamentos')->orderBy('id','ASC');
    }

    public function documentos()
    {
        return $this->hasMany('App\Documentos')->orderBy('id','ASC');
    }

    public static function queryGeral()
    {
        return Creditos::select('creditos.*', 'consumidores.nome AS consumidor', 'veiculos.descricao AS veiculo', 'veiculos.placa', 'vendedores.nome AS vendedor', 'clientes.nome AS cliente', 'veiculos.tipo as veiculotipo')
                    ->join('consumidores','consumidores.id','=','creditos.consumidores_id')
                    ->join('clientes','clientes.id','=','consumidores.clientes_id')
                    ->leftjoin('vendedores','vendedores.id','=','creditos.vendedores_id')
                    ->leftjoin('veiculos','veiculos.id','=','creditos.veiculos_id');
    }

}
