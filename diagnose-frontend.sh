#!/bin/bash

echo "🔍 Diagnóstico Completo do Frontend Wincash"
echo "==========================================="

BASE_URL="http://localhost:8001"

echo ""
echo "1️⃣ Testando conectividade básica..."
if curl -s --max-time 5 "$BASE_URL" > /dev/null; then
    echo "✅ Servidor respondendo"
else
    echo "❌ Servidor não responde"
    exit 1
fi

echo ""
echo "2️⃣ Verificando headers HTTP..."
headers=$(curl -I -s "$BASE_URL")
echo "$headers" | grep -E "HTTP|Content-Type|X-Frame|Content-Security"

echo ""
echo "3️⃣ Testando recursos críticos..."
critical_resources=(
    "/css/bootstrap.min.css"
    "/css/front.css"
    "/js/jquery-3.4.1.min.js"
    "/js/bootstrap.min.js"
    "/js/app.js"
)

for resource in "${critical_resources[@]}"; do
    status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$resource")
    size=$(curl -s -o /dev/null -w "%{size_download}" "$BASE_URL$resource")
    if [ "$status" = "200" ] && [ "$size" -gt 0 ]; then
        echo "✅ $resource (${size} bytes)"
    else
        echo "❌ $resource (Status: $status, Size: $size)"
    fi
done

echo ""
echo "4️⃣ Verificando estrutura HTML..."
html_content=$(curl -s "$BASE_URL")

# Verificar elementos essenciais
if echo "$html_content" | grep -q "<html"; then
    echo "✅ Tag HTML presente"
else
    echo "❌ Tag HTML ausente"
fi

if echo "$html_content" | grep -q "<head"; then
    echo "✅ Tag HEAD presente"
else
    echo "❌ Tag HEAD ausente"
fi

if echo "$html_content" | grep -q "<body"; then
    echo "✅ Tag BODY presente"
else
    echo "❌ Tag BODY ausente"
fi

echo ""
echo "5️⃣ Verificando conteúdo dinâmico..."
if echo "$html_content" | grep -q "Escolha uma unidade"; then
    echo "✅ Título principal encontrado"
else
    echo "❌ Título principal ausente"
fi

if echo "$html_content" | grep -q "Selecione sua cidade"; then
    echo "✅ Dropdown de cidades presente"
else
    echo "❌ Dropdown de cidades ausente"
fi

clients_found=$(echo "$html_content" | grep -c "unidadeitem")
echo "📊 Clientes encontrados: $clients_found"

echo ""
echo "6️⃣ Verificando JavaScript inline..."
if echo "$html_content" | grep -q "document.ready\|jQuery\|\$"; then
    echo "✅ JavaScript/jQuery detectado"
else
    echo "❌ JavaScript/jQuery não detectado"
fi

echo ""
echo "7️⃣ Verificando CSS inline/externo..."
css_links=$(echo "$html_content" | grep -c "rel=\"stylesheet\"")
echo "📊 Links CSS encontrados: $css_links"

echo ""
echo "8️⃣ Testando página de teste..."
test_status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/test.html")
if [ "$test_status" = "200" ]; then
    echo "✅ Página de teste acessível: $BASE_URL/test.html"
else
    echo "❌ Página de teste inacessível"
fi

echo ""
echo "9️⃣ Verificando logs do Nginx..."
echo "Últimas 5 linhas do log do Nginx:"
docker logs wincash_nginx --tail 5 2>/dev/null || echo "❌ Não foi possível acessar logs do Nginx"

echo ""
echo "🔟 Verificando logs da aplicação..."
echo "Últimas 5 linhas do log da aplicação:"
docker logs wincash_app --tail 5 2>/dev/null || echo "❌ Não foi possível acessar logs da aplicação"

echo ""
echo "📋 RESUMO DO DIAGNÓSTICO:"
echo "========================"
echo "🌐 URL Principal: $BASE_URL"
echo "🧪 URL de Teste: $BASE_URL/test.html"
echo "📊 Clientes carregados: $clients_found"
echo "🎨 Links CSS: $css_links"

if [ $clients_found -gt 0 ] && [ $css_links -gt 0 ]; then
    echo "✅ Frontend parece estar funcionando corretamente"
else
    echo "⚠️  Frontend pode ter problemas de renderização"
fi
