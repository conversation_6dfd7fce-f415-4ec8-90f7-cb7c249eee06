<?php

namespace App;

class Motoristas extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'motoristas';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'consumidores_id',
		'nome',
        'email',
        'celular',
        'nascimento',
        'status'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'consumidores_id'   => 'integer|required',
        'nome'          => 'required|max:254',
        'email'         => 'email|nullable',
        'nascimento'    => 'date|nullable'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim'
	);

    protected $dates = [
        'nascimento'
    ];

    public function consumidor()
    {
        return $this->hasOne('App\Consumidores','id','consumidores_id');
    }


}
