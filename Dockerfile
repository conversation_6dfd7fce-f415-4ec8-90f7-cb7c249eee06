FROM php:7.4-fpm

# Argumentos para não interação durante a instalação
ARG DEBIAN_FRONTEND=noninteractive
   
# Instalar dependências
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    zip \
    unzip \
    git \
    curl \
    libonig-dev \
    libxml2-dev \
    libmcrypt-dev \
    nodejs \
    npm \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install pdo_mysql zip exif pcntl bcmath opcache mbstring xml

# Instalar Composer
COPY --from=composer:2.0 /usr/bin/composer /usr/bin/composer
   
# Configurar o PHP para Laravel
RUN echo "memory_limit=512M" > /usr/local/etc/php/conf.d/memory-limit.ini \
    && echo "upload_max_filesize=64M" > /usr/local/etc/php/conf.d/upload-limit.ini \
    && echo "post_max_size=64M" >> /usr/local/etc/php/conf.d/upload-limit.ini

# Configurar diretório de trabalho
WORKDIR /var/www

# Garantir que www-data tenha UID e GID corretos
RUN usermod -u 1000 www-data && groupmod -g 1000 www-data

# Criar diretórios necessários e definir permissões
RUN mkdir -p /var/www/app/public/css \
    /var/www/app/public/js \
    /var/www/app/public/images \
    /var/www/app/storage/logs \
    /var/www/app/storage/framework/sessions \
    /var/www/app/storage/framework/views \
    /var/www/app/storage/framework/cache \
    /var/www/app/bootstrap/cache \
    && chown -R www-data:www-data /var/www \
    && find /var/www/app/public -type d -exec chmod 755 {} \; \
    && find /var/www/app/public -type f -exec chmod 644 {} \;

# Expor porta 9000 para PHP-FPM
EXPOSE 9000

CMD ["php-fpm"]
