



<?php $__env->startSection('content'); ?>



<div class="container text-center">

    <div class="text-center">
        <img src="<?php echo e(url('img/logo.png')); ?>" class="img-responsive mb-3" style="max-width:300px; max-height:120px" />
    </div>    

    <svg width="49" height="62" viewBox="0 0 49 62" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.10041 1.26076C3.98512 2.47303 3.93663 2.71549 3.93663 10.2801V18.0386L5.53683 19.4448C6.40966 20.2207 7.52495 20.8511 8.05835 20.8511C8.97968 20.8511 9.02817 21.1905 9.02817 25.9911C9.02817 30.6947 8.97968 31.1311 8.20382 30.7917C5.05192 29.4339 1.60907 30.3068 0.445292 32.7313C-0.282071 34.283 -0.23358 34.7194 1.4636 42.284C3.50021 51.0608 5.00343 54.6977 8.49477 59.0133L10.7738 61.8258L22.0722 61.9713C30.4126 62.0683 33.5645 61.9228 34.0979 61.4864C35.1647 60.6135 37.9287 54.8431 38.85 51.5942C39.9168 47.812 40.4987 30.3068 39.5774 28.1247C38.753 26.1851 36.9104 25.0698 34.2919 25.0213C32.9826 24.9728 31.4794 24.5849 30.7036 24.0515C29.7337 23.3726 28.8124 23.2271 27.3092 23.3726C25.709 23.5665 24.9332 23.3726 24.1088 22.7422C23.4784 22.2088 22.2662 21.8209 21.3448 21.8209C20.4235 21.8209 19.6961 21.5784 19.6961 21.336C19.6961 21.045 20.0356 20.8511 20.4235 20.8511C21.8297 20.8511 23.8664 19.4933 24.4483 18.1841C24.8362 17.4082 25.0301 14.3048 25.0301 9.94062C25.0301 3.1519 24.9816 2.95794 23.7694 1.50321L22.5086 -8.39373e-07H14.4106C6.31268 -8.39373e-07 6.26419 -8.39373e-07 5.10041 1.26076ZM20.666 10.1831C20.666 14.0623 20.472 16.4869 20.1811 16.4869C19.9386 16.4869 19.6961 15.614 19.6961 14.4988C19.6961 12.9955 19.4052 12.2197 18.2899 11.1529C14.7986 7.66155 9.70704 9.35873 9.17364 14.2078C8.64024 18.6205 8.05835 16.4384 8.05835 10.1831V3.87927H14.3622H20.666V10.1831ZM15.429 13.6744C15.4774 13.7714 15.6229 18.1841 15.7684 23.4696C16.0108 31.9555 16.1563 33.1677 16.8837 33.6526C18.5324 34.6709 19.4052 33.4587 19.6961 29.7734C19.8901 26.7669 20.0356 26.4275 21.0054 26.282C22.2662 26.0881 22.5086 26.8639 22.7026 31.0341C22.8481 33.4102 22.9935 33.7011 23.9633 33.8466C25.709 34.0891 26.4849 33.1192 26.4849 30.5492C26.4849 27.8337 27.4062 26.67 28.6185 27.8822C29.1519 28.4641 29.3943 29.5794 29.3943 31.81C29.3943 34.7194 29.4913 34.9134 30.6066 35.2043C32.4007 35.6408 33.2251 34.6709 33.3706 31.8585C33.516 29.7249 33.6615 29.337 34.4859 29.337C35.4072 29.337 35.4557 29.6764 35.4557 39.0351C35.4557 47.8605 35.3587 49.0242 34.3404 51.8852C33.7585 53.6309 32.8372 55.7645 32.3038 56.5888L31.3339 58.189H22.2662H13.1499L11.2587 55.619C8.49477 51.8852 7.33099 48.9757 5.6823 41.7991C3.88814 34.186 3.93663 34.4285 5.10041 34.4285C6.40966 34.4285 6.84608 35.2043 7.8159 39.7625C9.02817 45.2904 9.22213 45.6784 11.1133 45.4844L12.665 45.3389L12.9074 29.5794C13.0529 20.8995 13.2469 13.7714 13.2954 13.6744C13.3439 13.6259 13.8288 13.5774 14.3622 13.5774C14.8956 13.5774 15.3805 13.6259 15.429 13.6744Z" fill="#FFF"/>
        <path d="M28.376 1.26078C27.2607 2.47305 27.2122 2.71551 27.2122 10.2801V18.0386L28.8124 19.4448L30.3641 20.8511H37.4438C48.1118 20.8511 48.3057 20.6571 48.3057 9.94064C48.3057 3.15192 48.2572 2.95796 47.045 1.50323L45.7842 1.90213e-05H37.6862C29.5883 1.90213e-05 29.5398 1.90213e-05 28.376 1.26078ZM43.9416 10.1346C43.9416 15.3716 43.7961 16.4384 43.1657 16.6809C42.1959 17.0688 33.0796 17.0688 32.1098 16.6809C31.4794 16.4384 31.334 15.3716 31.334 10.1346V3.87929H37.6378H43.9416V10.1346Z" fill="#4DB287"/>
    </svg>

    <?php if(isset($template) and $template == 'clientes'): ?>
    <h2 style="padding: 15px 0 0 0" class="text-white">Escolha uma unidade</h2>

    <?php if(isset($cidades) and count($cidades) > 0): ?>
    <form action="<?php echo e(route('escolhaunidade_app')); ?>" method="POST" id="filtrarcidade">
        <?php echo e(csrf_field()); ?>

        <select name="cidade" id="cidade" class="form-control form-control-lg" onchange="$('#filtrarcidade').submit();">
            <option value="">Selecione sua cidade</option>
            <?php $__currentLoopData = $cidades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($item->cidade != ''): ?>
            <option value="<?php echo e($item->cidade); ?>" <?php if($item->cidade == $cidade): ?> selected="selected" <?php endif; ?>><?php echo e($item->cidade); ?> / <?php echo e($item->estado); ?></option>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php if(isset($promocoes) and count($promocoes) > 0): ?>
        <a href="<?php echo e(url('promocoes/'.$cidade)); ?>" class="btn meubgcor2 m-2 float-right">Promoções</a>
        <?php endif; ?>
    </form>
    <?php endif; ?>

    <?php endif; ?>
    <?php if(isset($template) and $template == 'promocoes'): ?>
    <h2 style="padding: 15px 0 0 0" class="text-white">
        Veja as promoções da cidade<br />
        <?php echo e($cidade); ?><br />
        <a href="<?php echo e(url('cidade/'.$cidade)); ?>" class="btn btn-sm meubgcor2 m-2">Unidades</a>
    </h2>
    <?php endif; ?>

</div>
<div class="clearfix"></div>

<div class="container container-fluid p-3 d-flex justify-content-center">


    <div id="escolhaunidade" class="text-center bg-white rounded shadow p-2 w-100" style="border: 1px solid #666;">


        <?php if(isset($clientes) and count($clientes) > 0 and 1==2): ?>

        <div class="mb-2">
            <div class="flexslider carousel">
                <ul class="slides">
                    <li>        
                        <button id="cat" class="btn btn-sm m-1 btncategoria">
                            <img src="<?php echo e(url('img/celular.jpg')); ?>" class="rounded-circle" style="max-width:80px" />
                            <br />
                            Todos
                        </button>    
                    </li>
                    <?php $__currentLoopData = $categorias; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoria): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>            
                        <button id="cat<?php echo e($categoria->id); ?>" class="btn btn-sm m-1 btncategoria">
                            <?php if($categoria->imagem): ?>
                            <img src="<?php echo e(url($categoria->imagem)); ?>" class="rounded-circle" style="max-width:80px" />
                            <?php else: ?>
                            <img src="<?php echo e(url('img/semimagem.jpg')); ?>" class="rounded-circle" style="max-width:80px" />
                            <?php endif; ?>    
                            <br />
                            <?php echo e($categoria->nome); ?>

                        </button>    
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <div class="custom-navigation">
                <a href="#" class="flex-prev minhacor"> <i class="fa fa-caret-left"></i> </a>
                <div class="custom-controls-container d-inline">&nbsp;&nbsp;&nbsp;</div>
                <a href="#" class="flex-next minhacor"> <i class="fa fa-caret-right"></i> </a>
            </div>

            <script>
                $(document).ready(function () {
                    $('.flexslider').flexslider({
                        animation: "slide",
                        controlNav: false,
                        directionNav: true,
                        itemWidth: 100,
                        controlsContainer: $(".custom-controls-container"),
                        customDirectionNav: $(".custom-navigation a")                        
                    });
                });
            </script>

        </div>
        
        <?php endif; ?>


        <div class="row">
        <div class="col-sm-3"></div>
        <div class="col-sm-6">

        <?php if(isset($clientes) and count($clientes) > 0): ?>

        <br />
        <?php $__currentLoopData = $clientes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cliente): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="cat cat<?php echo e($cliente->categorias_id); ?>">
            <a href="<?php echo e(route('cliente_app',['codigo'=>$cliente->url])); ?>" class="btn meubotao unidadeitem">
                <svg width="20" height="20" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 15px">
                    <circle cx="14.5" cy="14.5" r="13.5" stroke="#D9D9D9" stroke-width="2"/>
                </svg>
                <?php echo e($cliente->nome); ?>

            </a>
            <hr style="margin: 5px auto" />
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>

        <div class="mb-3">
        <?php
            $cat = array();
        ?>
        <?php $__currentLoopData = $promocoes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $promocao): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

            <?php if(!in_array($promocao->categorias_id, $cat)): ?>
            <button id="cat<?php echo e($promocao->categorias_id); ?>" class="btn btn-sm meubgcor m-1 btncategoria"><?php echo e($promocao->categoria); ?></button>
            <?php endif; ?>

            <?php
            $cat[] += $promocao->categorias_id;
            ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <?php $__currentLoopData = $promocoes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $promocao): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="cat cat<?php echo e($promocao->categorias_id); ?>">
            <div class="row justify-content-center">
            <div class="col-8">
            <?php if($promocao->imagem): ?>
            <a href="<?php echo e($promocao->link); ?>" target="_blank">
                <img src="<?php echo e(url($promocao->imagem)); ?>" class="img-fluid img-thumbnail" style="max-height: 300px" />
            </a>
            <?php endif; ?>
            <a href="<?php echo e($promocao->link); ?>" class="btn m-1" target="_blank">
                <?php echo e($promocao->nome); ?>

                <br />
                <?php echo e($promocao->titulo); ?>

                <br />
                <small>[+ clique para ver mais detalhes]</small>
            </a>
            </div>
            </div>
            <hr style="margin: 10px auto 15px" />
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <?php endif; ?>

        </div>
        <div class="col-sm-3"></div>
        </div>

        <img src="<?php echo e(url('img/moedas.png')); ?>" style="width: 35%; float: right; margin: 0 -5% -10% 0;" />

    </div>




</div>


<script>
$(document).ready(function()
{

 	$(document).delegate(".btncategoria", "click", function(){
        $('.btncategoria').removeClass('meubgcor2');
        $(this).addClass('meubgcor2');

        var idcat = $(this).attr('id');

        $('.cat').fadeOut();
        $('.'+idcat).fadeIn();

        return false;
    });

});

</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /var/www/app/resources/views/home.blade.php ENDPATH**/ ?>