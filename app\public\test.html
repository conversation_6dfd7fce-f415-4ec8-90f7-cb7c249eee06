<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Frontend - Wincash</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/front.css">
    <script src="/js/jquery-3.4.1.min.js"></script>
    <script src="/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="text-white">🧪 Teste Frontend Wincash</h1>
                <div class="bg-white p-4 rounded shadow mt-4">
                    <h2>Teste de Recursos</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>CSS</h4>
                            <div class="alert alert-success">✅ Bootstrap carregado</div>
                            <div class="alert alert-info">ℹ️ CSS customizado carregado</div>
                        </div>
                        <div class="col-md-6">
                            <h4>JavaScript</h4>
                            <div id="jquery-test" class="alert alert-warning">⏳ Testando jQuery...</div>
                            <div id="bootstrap-test" class="alert alert-warning">⏳ Testando Bootstrap...</div>
                        </div>
                    </div>
                    <button class="btn meubgcor" onclick="testFunction()">Testar Função</button>
                    <div id="test-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Teste jQuery
        $(document).ready(function() {
            $('#jquery-test').removeClass('alert-warning').addClass('alert-success').text('✅ jQuery funcionando');
            
            // Teste Bootstrap
            if (typeof bootstrap !== 'undefined' || typeof $.fn.modal !== 'undefined') {
                $('#bootstrap-test').removeClass('alert-warning').addClass('alert-success').text('✅ Bootstrap JS funcionando');
            } else {
                $('#bootstrap-test').removeClass('alert-warning').addClass('alert-danger').text('❌ Bootstrap JS com problema');
            }
        });

        function testFunction() {
            $('#test-result').html('<div class="alert alert-success">🎉 JavaScript funcionando perfeitamente!</div>');
        }

        // Teste de console
        console.log('🔍 Teste Frontend Wincash - Scripts carregados');
    </script>
</body>
</html>
